apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-django-backend-test-config
  labels:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/version: "fcd3e9b9"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-django-backend-test"
  PROJECT_ID: "ai-django-backend-test"
  APPLICATION_TYPE: "django-backend"
  SOURCE_REPO: "ChidhagniConsulting/ai-django-backend"
  SOURCE_BRANCH: "15/merge"
  COMMIT_SHA: "fcd3e9b9"
  
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:8000"
  API_URL: "http://localhost:8000/api"
  
  # Common Backend Configuration
  SERVER_PORT: "8000"

  

  
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "config.settings"
  DEBUG: "True"
  ALLOWED_HOSTS: "*************,localhost,127.0.0.1"

  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"

  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"

  # Django Database Configuration
  DATABASE_ENGINE: "django.db.backends.postgresql"
  DATABASE_CONN_MAX_AGE: "300"
  DATABASE_CONN_HEALTH_CHECKS: "True"
  DATABASE_OPTIONS_CONNECT_TIMEOUT: "30"
  DATABASE_OPTIONS_COMMAND_TIMEOUT: "60"
  DATABASE_OPTIONS_SERVER_SIDE_BINDING: "True"
  

  

  
