apiVersion: v1
kind: Service
metadata:
  name: ai-django-backend-test-service
  labels:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/version: "fcd3e9b9"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-django-backend-test
