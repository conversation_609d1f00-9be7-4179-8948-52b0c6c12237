apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-nest-backend-dev
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "1c702057"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: nest-backend
    source.repo: ChidhagniConsulting-ai-nest-backend
    source.branch: 18-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-nest-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-nest-backend/overlays/dev
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-nest-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for ai-nest-backend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/ai-nest-backend"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "nest-backend"
  - name: Source Branch
    value: "18/merge"
  - name: Commit SHA
    value: "1c702057"
  - name: Configuration
    value: "Development configuration with debug enabled"
