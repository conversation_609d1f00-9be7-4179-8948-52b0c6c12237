apiVersion: v1
kind: Secret
metadata:
  name: ai-django-backend-test-secrets
  labels:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/version: "fcd3e9b9"
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  

  
  DATABASE_URL: ********************************************************************************************************************************************************************************************************************
  

  

  # Essential Authentication Secrets
  JWT_SECRET: Y2VkMzU2NzJmNTU2YzBhZGU5M2RhNTAwZTdiNTc5YTliZjI1NDNmNjQ5OWMwODI0NjNkYmYxZmQ4Nzc2OGY5Mw==
  
  JWT_EXPIRES_IN: ODY0MDA=
  JWT_REFRESH_EXPIRES_IN: NjA0ODAw
  

  # Database Credentials (Development)
  DB_USER: ZGphbmdvX2Rldl91c2Vy
  DB_PASSWORD: QVZOU19Kd3ZJblJOTEFlMXBZRnBZWXRp
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: ZGphbmdvX2Rldl9kYg==
  DB_SSL_MODE: cmVxdWlyZQ==

  # SMTP Configuration (Development)
  SMTP_USER: ********************************************
  SMTP_PASS: ********************************************************************************************

  # OAuth2 Configuration (Development)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=

  
  # Django-specific secrets
  SESSION_SECRET: M24hQCM5ZDhmN2c2aDVqNGszbDJtMW4wYjl2OGM3eDZ6NWE0czNkMmYxZzBoOWo4azdsNm01bjRiM3YyYzE=
  RATE_LIMIT_WINDOW_MS: OTAwMDAw
  RATE_LIMIT_MAX_REQUESTS: MTAw
  PASSWORD_RESET_TOKEN_EXPIRY: MzYwMA==
  EMAIL_VERIFICATION_TOKEN_EXPIRY: MzYwMA==
  

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
